package com.example.jobrec
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.FirebaseFirestoreSettings
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.functions.FirebaseFunctions
import android.util.Log
import kotlin.random.Random
class FirebaseHelper private constructor() {
    private val db: FirebaseFirestore
    private val auth: FirebaseAuth
    private val functions: FirebaseFunctions
    private val usersCollection: com.google.firebase.firestore.CollectionReference
    private val companiesCollection: com.google.firebase.firestore.CollectionReference
    companion object {
        @Volatile
        private var INSTANCE: FirebaseHelper? = null
        private const val TAG = "FirebaseHelper"
        fun getInstance(): FirebaseHelper {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: FirebaseHelper().also { INSTANCE = it }
            }
        }
    }
    init {
        db = FirebaseFirestore.getInstance().apply {
            firestoreSettings = FirebaseFirestoreSettings.Builder()
                .setCacheSizeBytes(FirebaseFirestoreSettings.CACHE_SIZE_UNLIMITED)
                .build()
        }
        auth = FirebaseAuth.getInstance()
        functions = FirebaseFunctions.getInstance()
        usersCollection = db.collection("users")
        companiesCollection = db.collection("companies")
    }
    fun addUser(user: User, password: String, callback: (Boolean, String?) -> Unit) {
        auth.createUserWithEmailAndPassword(user.email, password)
            .addOnSuccessListener { authResult ->
                val userId = authResult.user?.uid
                if (userId != null) {
                    val userWithId = user.copy(id = userId)
                    usersCollection.document(userId)
                        .set(userWithId)
                        .addOnSuccessListener {
                            callback(true, null)
                        }
                        .addOnFailureListener { e ->
                            Log.e(TAG, "Error adding user to Firestore", e)
                            callback(false, e.message)
                        }
                } else {
                    Log.e(TAG, "Failed to get user ID after auth")
                    callback(false, "Failed to get user ID")
                }
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error creating user in Firebase Auth", e)
                callback(false, e.message)
            }
    }
    fun addCompany(company: Company, password: String, callback: (Boolean, String?) -> Unit) {
        auth.createUserWithEmailAndPassword(company.email, password)
            .addOnSuccessListener { authResult ->
                val userId = authResult.user?.uid
                if (userId != null) {
                    val companyWithUserId = company.copy(userId = userId, id = company.registrationNumber)
                    companiesCollection.document(company.registrationNumber)
                        .set(companyWithUserId)
                        .addOnSuccessListener {
                            Log.d(TAG, "Company added successfully with ID: ${company.registrationNumber}")
                            callback(true, null)
                        }
                        .addOnFailureListener { e ->
                            Log.e(TAG, "Error adding company to Firestore", e)
                            callback(false, e.message)
                        }
                } else {
                    Log.e(TAG, "Failed to get user ID after auth")
                    callback(false, "Failed to get user ID")
                }
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error creating company in Firebase Auth", e)
                callback(false, e.message)
            }
    }
    fun checkUser(email: String, password: String, callback: (Boolean, String?, String?) -> Unit) {
        auth.signInWithEmailAndPassword(email, password)
            .addOnSuccessListener { authResult ->
                val userEmail = email.lowercase()
                Log.d(TAG, "Looking for user with email (lowercase): $userEmail")
                usersCollection.get()
                    .addOnSuccessListener { userDocuments ->
                        val userDoc = userDocuments.find { doc ->
                            doc.getString("email")?.lowercase() == userEmail
                        }
                        if (userDoc != null) {
                            val emailVerified = userDoc.getBoolean("emailVerified") ?: false
                            if (emailVerified) {
                                callback(true, "user", null)
                            } else {
                                callback(false, null, "Please verify your email address before logging in")
                            }
                            return@addOnSuccessListener
                        }
                        companiesCollection.get()
                            .addOnSuccessListener { companyDocuments ->
                                val companyDoc = companyDocuments.find { doc ->
                                    doc.getString("email")?.lowercase() == userEmail
                                }
                                if (companyDoc != null) {
                                    val emailVerified = companyDoc.getBoolean("emailVerified") ?: false
                                    if (emailVerified) {
                                        callback(true, "company", null)
                                    } else {
                                        callback(false, null, "Please verify your email address before logging in")
                                    }
                                } else {
                                    callback(true, "unknown", null)
                                }
                            }
                            .addOnFailureListener { e ->
                                Log.e(TAG, "Error checking companies collection", e)
                                callback(false, null, e.message)
                            }
                    }
                    .addOnFailureListener { e ->
                        Log.e(TAG, "Error checking users collection", e)
                        callback(false, null, e.message)
                    }
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error signing in", e)
                callback(false, null, e.message)
            }
    }
    fun isEmailExists(email: String, callback: (Boolean) -> Unit) {
        val userEmail = email.lowercase()
        Log.d(TAG, "Checking if email exists (lowercase): $userEmail")
        usersCollection.get()
            .addOnSuccessListener { userDocuments ->
                val userDoc = userDocuments.find { doc ->
                    doc.getString("email")?.lowercase() == userEmail
                }
                if (userDoc != null) {
                    callback(true)
                    return@addOnSuccessListener
                }
                companiesCollection.get()
                    .addOnSuccessListener { companyDocuments ->
                        val companyDoc = companyDocuments.find { doc ->
                            doc.getString("email")?.lowercase() == userEmail
                        }
                        callback(companyDoc != null)
                    }
                    .addOnFailureListener { e ->
                        Log.e(TAG, "Error checking companies collection", e)
                        callback(false)
                    }
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error checking users collection", e)
                callback(false)
            }
    }
    fun isIdNumberExists(idNumber: String, callback: (Boolean) -> Unit) {
        usersCollection.document(idNumber)
            .get()
            .addOnSuccessListener { document ->
                callback(document.exists())
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error checking ID number", e)
                callback(false)
            }
    }
    fun recoverOrCreateUser(user: User, password: String, callback: (Boolean, String?) -> Unit) {
        auth.signInWithEmailAndPassword(user.email, password)
            .addOnSuccessListener { authResult ->
                val userId = authResult.user?.uid
                if (userId != null) {
                    usersCollection.document(userId)
                        .get()
                        .addOnSuccessListener { document ->
                            if (document.exists()) {
                                callback(true, null)
                            } else {
                                val userWithId = user.copy(id = userId)
                                usersCollection.document(userId)
                                    .set(userWithId)
                                    .addOnSuccessListener {
                                        callback(true, null)
                                    }
                                    .addOnFailureListener { e ->
                                        Log.e(TAG, "Failed to create Firestore record for existing Auth user", e)
                                        callback(false, e.message)
                                    }
                            }
                        }
                        .addOnFailureListener { e ->
                            Log.e(TAG, "Error checking for existing Firestore record", e)
                            callback(false, e.message)
                        }
                } else {
                    Log.e(TAG, "Failed to get user ID after sign in")
                    callback(false, "Failed to get user ID")
                }
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Failed to sign in to existing account", e)
                addUser(user, password, callback)
            }
    }
    fun recoverOrCreateCompany(company: Company, password: String, callback: (Boolean, String?) -> Unit) {
        auth.signInWithEmailAndPassword(company.email, password)
            .addOnSuccessListener { authResult ->
                val userId = authResult.user?.uid
                if (userId != null) {
                    val companyEmail = company.email.lowercase()
                    Log.d(TAG, "Checking if company exists with email (lowercase): $companyEmail")
                    companiesCollection.get()
                        .addOnSuccessListener { documents ->
                            val companyDoc = documents.find { doc ->
                                doc.getString("email")?.lowercase() == companyEmail
                            }
                            if (companyDoc != null) {
                                callback(true, null)
                            } else {
                                val companyWithUserId = company.copy(userId = userId, id = company.registrationNumber)
                                companiesCollection.document(company.registrationNumber)
                                    .set(companyWithUserId)
                                    .addOnSuccessListener {
                                        Log.d(TAG, "Company recovered successfully with ID: ${company.registrationNumber}")
                                        callback(true, null)
                                    }
                                    .addOnFailureListener { e ->
                                        Log.e(TAG, "Failed to create Firestore record for existing Auth company", e)
                                        callback(false, e.message)
                                    }
                            }
                        }
                        .addOnFailureListener { e ->
                            Log.e(TAG, "Error checking for existing Firestore record", e)
                            callback(false, e.message)
                        }
                } else {
                    Log.e(TAG, "Failed to get user ID after sign in")
                    callback(false, "Failed to get user ID")
                }
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Failed to sign in to existing account", e)
                addCompany(company, password, callback)
            }
    }

    // Email Verification Methods
    fun generateVerificationCode(): String {
        return Random.nextInt(100000, 999999).toString()
    }

    fun sendVerificationEmail(email: String, verificationCode: String, userType: String, name: String, callback: (Boolean, String?) -> Unit) {
        val data = hashMapOf(
            "email" to email,
            "verificationCode" to verificationCode,
            "userType" to userType,
            "name" to name
        )

        functions.getHttpsCallable("sendVerificationEmail")
            .call(data)
            .addOnSuccessListener { result ->
                Log.d(TAG, "Verification email sent successfully")
                callback(true, null)
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error sending verification email", e)
                callback(false, e.message)
            }
    }

    fun registerUserWithVerification(user: User, password: String, callback: (Boolean, String?, String?) -> Unit) {
        // Generate verification code
        val verificationCode = generateVerificationCode()
        val userWithCode = user.copy(verificationCode = verificationCode, emailVerified = false)

        // Create Firebase Auth user but don't sign them in
        auth.createUserWithEmailAndPassword(user.email, password)
            .addOnSuccessListener { authResult ->
                val userId = authResult.user?.uid
                if (userId != null) {
                    // Store user data with verification code
                    val userWithId = userWithCode.copy(id = userId)
                    usersCollection.document(userId)
                        .set(userWithId)
                        .addOnSuccessListener {
                            // Sign out the user immediately
                            auth.signOut()

                            // Send verification email
                            sendVerificationEmail(
                                user.email,
                                verificationCode,
                                "student",
                                user.name
                            ) { emailSent, emailError ->
                                if (emailSent) {
                                    callback(true, null, verificationCode)
                                } else {
                                    callback(false, "User created but failed to send verification email: $emailError", null)
                                }
                            }
                        }
                        .addOnFailureListener { e ->
                            Log.e(TAG, "Error adding user to Firestore", e)
                            callback(false, e.message, null)
                        }
                } else {
                    Log.e(TAG, "Failed to get user ID after auth")
                    callback(false, "Failed to get user ID", null)
                }
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error creating user in Firebase Auth", e)
                callback(false, e.message, null)
            }
    }

    fun registerCompanyWithVerification(company: Company, password: String, callback: (Boolean, String?, String?) -> Unit) {
        // Generate verification code
        val verificationCode = generateVerificationCode()
        val companyWithCode = company.copy(verificationCode = verificationCode, emailVerified = false)

        // Create Firebase Auth user but don't sign them in
        auth.createUserWithEmailAndPassword(company.email, password)
            .addOnSuccessListener { authResult ->
                val userId = authResult.user?.uid
                if (userId != null) {
                    // Store company data with verification code
                    val companyWithUserId = companyWithCode.copy(userId = userId, id = company.registrationNumber)
                    companiesCollection.document(company.registrationNumber)
                        .set(companyWithUserId)
                        .addOnSuccessListener {
                            // Sign out the user immediately
                            auth.signOut()

                            // Send verification email
                            sendVerificationEmail(
                                company.email,
                                verificationCode,
                                "company",
                                company.companyName
                            ) { emailSent, emailError ->
                                if (emailSent) {
                                    callback(true, null, verificationCode)
                                } else {
                                    callback(false, "Company created but failed to send verification email: $emailError", null)
                                }
                            }
                        }
                        .addOnFailureListener { e ->
                            Log.e(TAG, "Error adding company to Firestore", e)
                            callback(false, e.message, null)
                        }
                } else {
                    Log.e(TAG, "Failed to get user ID after auth")
                    callback(false, "Failed to get user ID", null)
                }
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error creating company in Firebase Auth", e)
                callback(false, e.message, null)
            }
    }

    fun verifyEmailCode(email: String, enteredCode: String, callback: (Boolean, String?, String?) -> Unit) {
        val userEmail = email.lowercase()

        // Check users collection first
        usersCollection.get()
            .addOnSuccessListener { userDocuments ->
                val userDoc = userDocuments.find { doc ->
                    doc.getString("email")?.lowercase() == userEmail
                }

                if (userDoc != null) {
                    val storedCode = userDoc.getString("verificationCode")
                    if (storedCode == enteredCode) {
                        // Update user as verified
                        usersCollection.document(userDoc.id)
                            .update(mapOf(
                                "emailVerified" to true,
                                "verificationCode" to ""
                            ))
                            .addOnSuccessListener {
                                callback(true, "user", null)
                            }
                            .addOnFailureListener { e ->
                                callback(false, null, e.message)
                            }
                    } else {
                        callback(false, null, "Invalid verification code")
                    }
                    return@addOnSuccessListener
                }

                // Check companies collection
                companiesCollection.get()
                    .addOnSuccessListener { companyDocuments ->
                        val companyDoc = companyDocuments.find { doc ->
                            doc.getString("email")?.lowercase() == userEmail
                        }

                        if (companyDoc != null) {
                            val storedCode = companyDoc.getString("verificationCode")
                            if (storedCode == enteredCode) {
                                // Update company as verified
                                companiesCollection.document(companyDoc.id)
                                    .update(mapOf(
                                        "emailVerified" to true,
                                        "verificationCode" to ""
                                    ))
                                    .addOnSuccessListener {
                                        callback(true, "company", null)
                                    }
                                    .addOnFailureListener { e ->
                                        callback(false, null, e.message)
                                    }
                            } else {
                                callback(false, null, "Invalid verification code")
                            }
                        } else {
                            callback(false, null, "Email not found")
                        }
                    }
                    .addOnFailureListener { e ->
                        callback(false, null, e.message)
                    }
            }
            .addOnFailureListener { e ->
                callback(false, null, e.message)
            }
    }

    // Password Reset Methods
    fun sendPasswordResetCode(email: String, callback: (Boolean, String?) -> Unit) {
        val userEmail = email.lowercase()
        val resetCode = generateVerificationCode()
        val expiryTime = System.currentTimeMillis() + (15 * 60 * 1000) // 15 minutes

        // Check users collection first
        usersCollection.get()
            .addOnSuccessListener { userDocuments ->
                val userDoc = userDocuments.find { doc ->
                    doc.getString("email")?.lowercase() == userEmail
                }

                if (userDoc != null) {
                    val userData = userDoc.data
                    val userName = "${userData?.get("name") ?: ""} ${userData?.get("surname") ?: ""}".trim()

                    // Update user with reset code
                    usersCollection.document(userDoc.id)
                        .update(mapOf(
                            "passwordResetCode" to resetCode,
                            "passwordResetExpiry" to expiryTime
                        ))
                        .addOnSuccessListener {
                            // Send reset email
                            sendPasswordResetEmail(email, resetCode, "student", userName) { emailSent, emailError ->
                                callback(emailSent, emailError)
                            }
                        }
                        .addOnFailureListener { e ->
                            callback(false, e.message)
                        }
                    return@addOnSuccessListener
                }

                // Check companies collection
                companiesCollection.get()
                    .addOnSuccessListener { companyDocuments ->
                        val companyDoc = companyDocuments.find { doc ->
                            doc.getString("email")?.lowercase() == userEmail
                        }

                        if (companyDoc != null) {
                            val companyData = companyDoc.data
                            val companyName = companyData?.get("companyName") as? String ?: ""

                            // Update company with reset code
                            companiesCollection.document(companyDoc.id)
                                .update(mapOf(
                                    "passwordResetCode" to resetCode,
                                    "passwordResetExpiry" to expiryTime
                                ))
                                .addOnSuccessListener {
                                    // Send reset email
                                    sendPasswordResetEmail(email, resetCode, "company", companyName) { emailSent, emailError ->
                                        callback(emailSent, emailError)
                                    }
                                }
                                .addOnFailureListener { e ->
                                    callback(false, e.message)
                                }
                        } else {
                            callback(false, "Email not found")
                        }
                    }
                    .addOnFailureListener { e ->
                        callback(false, e.message)
                    }
            }
            .addOnFailureListener { e ->
                callback(false, e.message)
            }
    }

    private fun sendPasswordResetEmail(email: String, resetCode: String, userType: String, name: String, callback: (Boolean, String?) -> Unit) {
        val data = hashMapOf(
            "email" to email,
            "resetCode" to resetCode,
            "userType" to userType,
            "name" to name
        )

        functions.getHttpsCallable("sendPasswordResetEmail")
            .call(data)
            .addOnSuccessListener { result ->
                Log.d(TAG, "Password reset email sent successfully")
                callback(true, null)
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error sending password reset email", e)
                callback(false, e.message)
            }
    }

    fun verifyPasswordResetCode(email: String, enteredCode: String, callback: (Boolean, String?) -> Unit) {
        val userEmail = email.lowercase()
        val currentTime = System.currentTimeMillis()

        // Check users collection first
        usersCollection.get()
            .addOnSuccessListener { userDocuments ->
                val userDoc = userDocuments.find { doc ->
                    doc.getString("email")?.lowercase() == userEmail
                }

                if (userDoc != null) {
                    val storedCode = userDoc.getString("passwordResetCode")
                    val expiryTime = userDoc.getLong("passwordResetExpiry") ?: 0

                    when {
                        storedCode != enteredCode -> callback(false, "Invalid reset code")
                        currentTime > expiryTime -> callback(false, "Reset code has expired")
                        else -> callback(true, null)
                    }
                    return@addOnSuccessListener
                }

                // Check companies collection
                companiesCollection.get()
                    .addOnSuccessListener { companyDocuments ->
                        val companyDoc = companyDocuments.find { doc ->
                            doc.getString("email")?.lowercase() == userEmail
                        }

                        if (companyDoc != null) {
                            val storedCode = companyDoc.getString("passwordResetCode")
                            val expiryTime = companyDoc.getLong("passwordResetExpiry") ?: 0

                            when {
                                storedCode != enteredCode -> callback(false, "Invalid reset code")
                                currentTime > expiryTime -> callback(false, "Reset code has expired")
                                else -> callback(true, null)
                            }
                        } else {
                            callback(false, "Email not found")
                        }
                    }
                    .addOnFailureListener { e ->
                        callback(false, e.message)
                    }
            }
            .addOnFailureListener { e ->
                callback(false, e.message)
            }
    }

    fun resetPassword(email: String, newPassword: String, callback: (Boolean, String?) -> Unit) {
        // First sign in with the current user to get access to update password
        auth.sendPasswordResetEmail(email)
            .addOnSuccessListener {
                // Clear reset codes from database
                clearPasswordResetCodes(email)
                callback(true, null)
            }
            .addOnFailureListener { e ->
                callback(false, e.message)
            }
    }

    private fun clearPasswordResetCodes(email: String) {
        val userEmail = email.lowercase()

        // Clear from users collection
        usersCollection.get()
            .addOnSuccessListener { userDocuments ->
                val userDoc = userDocuments.find { doc ->
                    doc.getString("email")?.lowercase() == userEmail
                }

                userDoc?.let {
                    usersCollection.document(it.id)
                        .update(mapOf(
                            "passwordResetCode" to "",
                            "passwordResetExpiry" to 0
                        ))
                }
            }

        // Clear from companies collection
        companiesCollection.get()
            .addOnSuccessListener { companyDocuments ->
                val companyDoc = companyDocuments.find { doc ->
                    doc.getString("email")?.lowercase() == userEmail
                }

                companyDoc?.let {
                    companiesCollection.document(it.id)
                        .update(mapOf(
                            "passwordResetCode" to "",
                            "passwordResetExpiry" to 0
                        ))
                }
            }
    }
}