package com.example.jobrec

import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.text.Editable
import android.text.TextWatcher
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.google.android.material.textfield.TextInputEditText

class EmailVerificationActivity : AppCompatActivity() {
    
    private lateinit var emailDisplay: TextView
    private lateinit var verificationCodeInput: TextInputEditText
    private lateinit var verifyButton: Button
    private lateinit var resendButton: Button
    private lateinit var timerText: TextView
    private lateinit var instructionText: TextView
    
    private var email: String = ""
    private var userType: String = ""
    private var userName: String = ""
    private var countDownTimer: CountDownTimer? = null
    private var canResend = false
    
    companion object {
        const val EXTRA_EMAIL = "email"
        const val EXTRA_USER_TYPE = "user_type"
        const val EXTRA_USER_NAME = "user_name"
        private const val RESEND_COOLDOWN = 60000L // 60 seconds
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_email_verification)
        
        // Get data from intent
        email = intent.getStringExtra(EXTRA_EMAIL) ?: ""
        userType = intent.getStringExtra(EXTRA_USER_TYPE) ?: ""
        userName = intent.getStringExtra(EXTRA_USER_NAME) ?: ""
        
        if (email.isEmpty()) {
            Toast.makeText(this, "Error: No email provided", Toast.LENGTH_SHORT).show()
            finish()
            return
        }
        
        initializeViews()
        setupUI()
        setupListeners()
        startResendTimer()
    }
    
    private fun initializeViews() {
        emailDisplay = findViewById(R.id.emailDisplay)
        verificationCodeInput = findViewById(R.id.verificationCodeInput)
        verifyButton = findViewById(R.id.verifyButton)
        resendButton = findViewById(R.id.resendButton)
        timerText = findViewById(R.id.timerText)
        instructionText = findViewById(R.id.instructionText)
    }
    
    private fun setupUI() {
        emailDisplay.text = email
        instructionText.text = "We've sent a 6-digit verification code to your email address. Please enter it below to verify your ${userType} account."
        
        // Initially disable resend button
        resendButton.isEnabled = false
        resendButton.alpha = 0.5f
    }
    
    private fun setupListeners() {
        verificationCodeInput.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                val code = s.toString().trim()
                verifyButton.isEnabled = code.length == 6
                verifyButton.alpha = if (code.length == 6) 1.0f else 0.5f
                
                // Auto-verify when 6 digits are entered
                if (code.length == 6) {
                    verifyCode(code)
                }
            }
        })
        
        verifyButton.setOnClickListener {
            val code = verificationCodeInput.text.toString().trim()
            if (code.length == 6) {
                verifyCode(code)
            } else {
                Toast.makeText(this, "Please enter a 6-digit verification code", Toast.LENGTH_SHORT).show()
            }
        }
        
        resendButton.setOnClickListener {
            if (canResend) {
                resendVerificationCode()
            }
        }
        
        findViewById<TextView>(R.id.backToLoginText).setOnClickListener {
            finish()
        }
    }
    
    private fun verifyCode(code: String) {
        verifyButton.isEnabled = false
        verifyButton.text = "Verifying..."
        
        FirebaseHelper.getInstance().verifyEmailCode(email, code) { success, detectedUserType, error ->
            runOnUiThread {
                verifyButton.isEnabled = true
                verifyButton.text = "Verify"
                
                if (success) {
                    Toast.makeText(this, "Email verified successfully!", Toast.LENGTH_SHORT).show()
                    
                    // Navigate to appropriate screen based on user type
                    when (detectedUserType) {
                        "user" -> {
                            val intent = Intent(this, LoginActivity::class.java)
                            intent.putExtra("verified_email", email)
                            intent.putExtra("user_type", "student")
                            startActivity(intent)
                        }
                        "company" -> {
                            val intent = Intent(this, LoginActivity::class.java)
                            intent.putExtra("verified_email", email)
                            intent.putExtra("user_type", "company")
                            startActivity(intent)
                        }
                        else -> {
                            val intent = Intent(this, LoginActivity::class.java)
                            startActivity(intent)
                        }
                    }
                    finish()
                } else {
                    Toast.makeText(this, error ?: "Verification failed", Toast.LENGTH_SHORT).show()
                    verificationCodeInput.text?.clear()
                }
            }
        }
    }
    
    private fun resendVerificationCode() {
        resendButton.isEnabled = false
        resendButton.text = "Sending..."
        
        val verificationCode = FirebaseHelper.getInstance().generateVerificationCode()
        
        FirebaseHelper.getInstance().sendVerificationEmail(email, verificationCode, userType, userName) { success, error ->
            runOnUiThread {
                if (success) {
                    Toast.makeText(this, "Verification code sent!", Toast.LENGTH_SHORT).show()
                    startResendTimer()
                } else {
                    Toast.makeText(this, "Failed to send code: $error", Toast.LENGTH_SHORT).show()
                    resendButton.isEnabled = true
                    resendButton.text = "Resend Code"
                }
            }
        }
    }
    
    private fun startResendTimer() {
        canResend = false
        resendButton.isEnabled = false
        resendButton.alpha = 0.5f
        
        countDownTimer?.cancel()
        countDownTimer = object : CountDownTimer(RESEND_COOLDOWN, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                val seconds = millisUntilFinished / 1000
                timerText.text = "Resend code in ${seconds}s"
                resendButton.text = "Resend Code (${seconds}s)"
            }
            
            override fun onFinish() {
                canResend = true
                resendButton.isEnabled = true
                resendButton.alpha = 1.0f
                resendButton.text = "Resend Code"
                timerText.text = "Didn't receive the code?"
            }
        }.start()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        countDownTimer?.cancel()
    }
}
