package com.example.jobrec

import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import java.util.regex.Pattern

class ResetPasswordActivity : AppCompatActivity() {
    
    private lateinit var emailDisplay: TextView
    private lateinit var newPasswordInput: TextInputEditText
    private lateinit var confirmPasswordInput: TextInputEditText
    private lateinit var newPasswordLayout: TextInputLayout
    private lateinit var confirmPasswordLayout: TextInputLayout
    private lateinit var resetButton: Button
    private lateinit var passwordStrengthText: TextView
    
    private var email: String = ""
    private var resetCode: String = ""
    
    companion object {
        const val EXTRA_EMAIL = "email"
        const val EXTRA_RESET_CODE = "reset_code"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_reset_password)
        
        // Get data from intent
        email = intent.getStringExtra(EXTRA_EMAIL) ?: ""
        resetCode = intent.getStringExtra(EXTRA_RESET_CODE) ?: ""
        
        if (email.isEmpty() || resetCode.isEmpty()) {
            Toast.makeText(this, "Error: Missing required data", Toast.LENGTH_SHORT).show()
            finish()
            return
        }
        
        initializeViews()
        setupUI()
        setupListeners()
    }
    
    private fun initializeViews() {
        emailDisplay = findViewById(R.id.emailDisplay)
        newPasswordInput = findViewById(R.id.newPasswordInput)
        confirmPasswordInput = findViewById(R.id.confirmPasswordInput)
        newPasswordLayout = findViewById(R.id.newPasswordLayout)
        confirmPasswordLayout = findViewById(R.id.confirmPasswordLayout)
        resetButton = findViewById(R.id.resetButton)
        passwordStrengthText = findViewById(R.id.passwordStrengthText)
        
        findViewById<androidx.appcompat.widget.Toolbar>(R.id.toolbar).setNavigationOnClickListener {
            finish()
        }
    }
    
    private fun setupUI() {
        emailDisplay.text = email
        resetButton.isEnabled = false
        resetButton.alpha = 0.5f
    }
    
    private fun setupListeners() {
        newPasswordInput.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                val password = s.toString()
                validatePassword(password)
                checkFormValidity()
            }
        })
        
        confirmPasswordInput.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                validatePasswordMatch()
                checkFormValidity()
            }
        })
        
        resetButton.setOnClickListener {
            resetPassword()
        }
    }
    
    private fun validatePassword(password: String) {
        val strength = getPasswordStrength(password)
        
        when (strength) {
            PasswordStrength.WEAK -> {
                passwordStrengthText.text = "Weak password"
                passwordStrengthText.setTextColor(getColor(android.R.color.holo_red_dark))
                newPasswordLayout.error = "Password must be at least 8 characters with uppercase, lowercase, number, and special character"
            }
            PasswordStrength.MEDIUM -> {
                passwordStrengthText.text = "Medium strength"
                passwordStrengthText.setTextColor(getColor(android.R.color.holo_orange_dark))
                newPasswordLayout.error = null
            }
            PasswordStrength.STRONG -> {
                passwordStrengthText.text = "Strong password"
                passwordStrengthText.setTextColor(getColor(android.R.color.holo_green_dark))
                newPasswordLayout.error = null
            }
        }
    }
    
    private fun validatePasswordMatch() {
        val password = newPasswordInput.text.toString()
        val confirmPassword = confirmPasswordInput.text.toString()
        
        if (confirmPassword.isNotEmpty() && password != confirmPassword) {
            confirmPasswordLayout.error = "Passwords do not match"
        } else {
            confirmPasswordLayout.error = null
        }
    }
    
    private fun checkFormValidity() {
        val password = newPasswordInput.text.toString()
        val confirmPassword = confirmPasswordInput.text.toString()
        
        val isPasswordStrong = getPasswordStrength(password) != PasswordStrength.WEAK
        val doPasswordsMatch = password == confirmPassword && password.isNotEmpty()
        
        val isValid = isPasswordStrong && doPasswordsMatch
        
        resetButton.isEnabled = isValid
        resetButton.alpha = if (isValid) 1.0f else 0.5f
    }
    
    private fun getPasswordStrength(password: String): PasswordStrength {
        if (password.length < 8) return PasswordStrength.WEAK
        
        val hasUppercase = password.any { it.isUpperCase() }
        val hasLowercase = password.any { it.isLowerCase() }
        val hasDigit = password.any { it.isDigit() }
        val hasSpecialChar = Pattern.compile("[^a-zA-Z0-9]").matcher(password).find()
        
        val criteriaCount = listOf(hasUppercase, hasLowercase, hasDigit, hasSpecialChar).count { it }
        
        return when {
            criteriaCount < 3 -> PasswordStrength.WEAK
            criteriaCount == 3 -> PasswordStrength.MEDIUM
            else -> PasswordStrength.STRONG
        }
    }
    
    private fun resetPassword() {
        val newPassword = newPasswordInput.text.toString()
        
        resetButton.isEnabled = false
        resetButton.text = "Resetting..."
        
        FirebaseHelper.getInstance().resetPassword(email, newPassword) { success, error ->
            runOnUiThread {
                resetButton.isEnabled = true
                resetButton.text = "Reset Password"
                
                if (success) {
                    Toast.makeText(this, "Password reset successfully! Please check your email for further instructions.", Toast.LENGTH_LONG).show()
                    
                    // Navigate back to login
                    val intent = Intent(this, LoginActivity::class.java)
                    intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK
                    intent.putExtra("reset_success", true)
                    intent.putExtra("email", email)
                    startActivity(intent)
                    finish()
                } else {
                    Toast.makeText(this, error ?: "Failed to reset password", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }
    
    private enum class PasswordStrength {
        WEAK, MEDIUM, STRONG
    }
}
