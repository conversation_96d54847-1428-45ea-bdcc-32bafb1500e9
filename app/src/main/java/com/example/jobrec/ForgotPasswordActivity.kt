package com.example.jobrec

import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.text.Editable
import android.text.TextWatcher
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.google.android.material.textfield.TextInputEditText

class ForgotPasswordActivity : AppCompatActivity() {
    
    private lateinit var emailInput: TextInputEditText
    private lateinit var sendCodeButton: Button
    private lateinit var verificationCodeInput: TextInputEditText
    private lateinit var verifyCodeButton: Button
    private lateinit var resendButton: Button
    private lateinit var timerText: TextView
    private lateinit var instructionText: TextView
    private lateinit var codeSection: androidx.constraintlayout.widget.ConstraintLayout
    
    private var countDownTimer: CountDownTimer? = null
    private var canResend = false
    private var currentEmail = ""
    
    companion object {
        private const val RESEND_COOLDOWN = 60000L // 60 seconds
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_forgot_password)
        
        initializeViews()
        setupListeners()
        
        // Initially hide the verification code section
        codeSection.visibility = android.view.View.GONE
    }
    
    private fun initializeViews() {
        emailInput = findViewById(R.id.emailInput)
        sendCodeButton = findViewById(R.id.sendCodeButton)
        verificationCodeInput = findViewById(R.id.verificationCodeInput)
        verifyCodeButton = findViewById(R.id.verifyCodeButton)
        resendButton = findViewById(R.id.resendButton)
        timerText = findViewById(R.id.timerText)
        instructionText = findViewById(R.id.instructionText)
        codeSection = findViewById(R.id.codeSection)
    }
    
    private fun setupListeners() {
        emailInput.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                val email = s.toString().trim()
                sendCodeButton.isEnabled = android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()
                sendCodeButton.alpha = if (sendCodeButton.isEnabled) 1.0f else 0.5f
            }
        })
        
        verificationCodeInput.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                val code = s.toString().trim()
                verifyCodeButton.isEnabled = code.length == 6
                verifyCodeButton.alpha = if (code.length == 6) 1.0f else 0.5f
                
                // Auto-verify when 6 digits are entered
                if (code.length == 6) {
                    verifyResetCode(code)
                }
            }
        })
        
        sendCodeButton.setOnClickListener {
            val email = emailInput.text.toString().trim()
            if (android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
                sendPasswordResetCode(email)
            } else {
                Toast.makeText(this, "Please enter a valid email address", Toast.LENGTH_SHORT).show()
            }
        }
        
        verifyCodeButton.setOnClickListener {
            val code = verificationCodeInput.text.toString().trim()
            if (code.length == 6) {
                verifyResetCode(code)
            } else {
                Toast.makeText(this, "Please enter a 6-digit verification code", Toast.LENGTH_SHORT).show()
            }
        }
        
        resendButton.setOnClickListener {
            if (canResend && currentEmail.isNotEmpty()) {
                sendPasswordResetCode(currentEmail)
            }
        }
        
        findViewById<TextView>(R.id.backToLoginText).setOnClickListener {
            finish()
        }
        
        findViewById<androidx.appcompat.widget.Toolbar>(R.id.toolbar).setNavigationOnClickListener {
            finish()
        }
    }
    
    private fun sendPasswordResetCode(email: String) {
        sendCodeButton.isEnabled = false
        sendCodeButton.text = "Sending..."
        currentEmail = email
        
        FirebaseHelper.getInstance().sendPasswordResetCode(email) { success, error ->
            runOnUiThread {
                sendCodeButton.isEnabled = true
                sendCodeButton.text = "Send Reset Code"
                
                if (success) {
                    Toast.makeText(this, "Reset code sent to your email!", Toast.LENGTH_SHORT).show()
                    showCodeSection()
                    startResendTimer()
                } else {
                    Toast.makeText(this, error ?: "Failed to send reset code", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }
    
    private fun verifyResetCode(code: String) {
        verifyCodeButton.isEnabled = false
        verifyCodeButton.text = "Verifying..."
        
        FirebaseHelper.getInstance().verifyPasswordResetCode(currentEmail, code) { success, error ->
            runOnUiThread {
                verifyCodeButton.isEnabled = true
                verifyCodeButton.text = "Verify Code"
                
                if (success) {
                    Toast.makeText(this, "Code verified! Redirecting to reset password...", Toast.LENGTH_SHORT).show()
                    
                    // Navigate to password reset screen
                    val intent = Intent(this, ResetPasswordActivity::class.java)
                    intent.putExtra("email", currentEmail)
                    intent.putExtra("reset_code", code)
                    startActivity(intent)
                    finish()
                } else {
                    Toast.makeText(this, error ?: "Invalid or expired code", Toast.LENGTH_SHORT).show()
                    verificationCodeInput.text?.clear()
                }
            }
        }
    }
    
    private fun showCodeSection() {
        codeSection.visibility = android.view.View.VISIBLE
        instructionText.text = "We've sent a 6-digit reset code to $currentEmail. Enter it below to continue."
        
        // Scroll to show the code section
        findViewById<androidx.core.widget.NestedScrollView>(R.id.scrollView).post {
            findViewById<androidx.core.widget.NestedScrollView>(R.id.scrollView).smoothScrollTo(0, codeSection.top)
        }
    }
    
    private fun startResendTimer() {
        canResend = false
        resendButton.isEnabled = false
        resendButton.alpha = 0.5f
        
        countDownTimer?.cancel()
        countDownTimer = object : CountDownTimer(RESEND_COOLDOWN, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                val seconds = millisUntilFinished / 1000
                timerText.text = "Resend code in ${seconds}s"
                resendButton.text = "Resend Code (${seconds}s)"
            }
            
            override fun onFinish() {
                canResend = true
                resendButton.isEnabled = true
                resendButton.alpha = 1.0f
                resendButton.text = "Resend Code"
                timerText.text = "Didn't receive the code?"
            }
        }.start()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        countDownTimer?.cancel()
    }
}
