<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_color">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary_color"
        app:title="Reset Password"
        app:titleTextColor="@android:color/white"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:navigationIconTint="@android:color/white" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Header -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="40dp">

                <ImageView
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:src="@drawable/ic_lock_reset"
                    android:layout_marginBottom="16dp"
                    app:tint="@color/primary_color" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Forgot Password?"
                    android:textSize="28sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/instructionText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Enter your email address and we'll send you a verification code to reset your password."
                    android:textSize="16sp"
                    android:textColor="@color/text_secondary"
                    android:textAlignment="center"
                    android:lineSpacingExtra="4dp" />

            </LinearLayout>

            <!-- Email Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:boxStrokeColor="@color/primary_color"
                app:hintTextColor="@color/primary_color"
                app:startIconDrawable="@drawable/ic_email"
                app:startIconTint="@color/primary_color"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/emailInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Email Address"
                    android:inputType="textEmailAddress"
                    android:textSize="16sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Send Code Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/sendCodeButton"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="Send Reset Code"
                android:textSize="16sp"
                android:textStyle="bold"
                android:backgroundTint="@color/primary_color"
                android:textColor="@android:color/white"
                android:layout_marginBottom="32dp"
                android:enabled="false"
                android:alpha="0.5"
                app:cornerRadius="12dp" />

            <!-- Verification Code Section (Initially Hidden) -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/codeSection"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone">

                <!-- Verification Code Input -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/codeInputLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="24dp"
                    app:boxStrokeColor="@color/primary_color"
                    app:hintTextColor="@color/primary_color"
                    app:startIconDrawable="@drawable/ic_security"
                    app:startIconTint="@color/primary_color"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/verificationCodeInput"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Enter 6-digit code"
                        android:inputType="number"
                        android:maxLength="6"
                        android:textSize="18sp"
                        android:textAlignment="center"
                        android:letterSpacing="0.3"
                        android:textStyle="bold" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Verify Code Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/verifyCodeButton"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="Verify Code"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:backgroundTint="@color/primary_color"
                    android:textColor="@android:color/white"
                    android:layout_marginBottom="24dp"
                    android:enabled="false"
                    android:alpha="0.5"
                    app:cornerRadius="12dp"
                    app:layout_constraintTop_toBottomOf="@id/codeInputLayout"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent" />

                <!-- Timer and Resend Section -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:layout_marginBottom="32dp"
                    app:layout_constraintTop_toBottomOf="@id/verifyCodeButton"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent">

                    <TextView
                        android:id="@+id/timerText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Didn't receive the code?"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/resendButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Resend Code"
                        android:textSize="14sp"
                        android:textColor="@color/primary_color"
                        android:backgroundTint="@android:color/transparent"
                        android:enabled="false"
                        android:alpha="0.5"
                        style="@style/Widget.MaterialComponents.Button.TextButton" />

                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Spacer -->
            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <!-- Back to Login -->
            <TextView
                android:id="@+id/backToLoginText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="Back to Login"
                android:textSize="16sp"
                android:textColor="@color/primary_color"
                android:textStyle="bold"
                android:padding="12dp"
                android:background="?android:attr/selectableItemBackground" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>
