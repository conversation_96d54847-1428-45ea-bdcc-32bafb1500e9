<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_color">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary_color"
        app:title="Reset Password"
        app:titleTextColor="@android:color/white"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:navigationIconTint="@android:color/white" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Header -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="40dp">

                <ImageView
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:src="@drawable/ic_lock_reset"
                    android:layout_marginBottom="16dp"
                    app:tint="@color/primary_color" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Create New Password"
                    android:textSize="28sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Create a strong password for your account. Make sure it's at least 8 characters long and includes uppercase, lowercase, numbers, and special characters."
                    android:textSize="16sp"
                    android:textColor="@color/text_secondary"
                    android:textAlignment="center"
                    android:lineSpacing="4dp" />

            </LinearLayout>

            <!-- Email Display -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="@color/border_color">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_email"
                        app:tint="@color/primary_color"
                        android:layout_marginEnd="12dp" />

                    <TextView
                        android:id="@+id/emailDisplay"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="<EMAIL>"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary"
                        android:textStyle="bold" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- New Password Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/newPasswordLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                app:boxStrokeColor="@color/primary_color"
                app:hintTextColor="@color/primary_color"
                app:startIconDrawable="@drawable/ic_lock"
                app:startIconTint="@color/primary_color"
                app:endIconMode="password_toggle"
                app:endIconTint="@color/primary_color"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/newPasswordInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="New Password"
                    android:inputType="textPassword"
                    android:textSize="16sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Password Strength Indicator -->
            <TextView
                android:id="@+id/passwordStrengthText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=""
                android:textSize="14sp"
                android:layout_marginBottom="16dp"
                android:layout_marginStart="16dp" />

            <!-- Confirm Password Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/confirmPasswordLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="32dp"
                app:boxStrokeColor="@color/primary_color"
                app:hintTextColor="@color/primary_color"
                app:startIconDrawable="@drawable/ic_lock"
                app:startIconTint="@color/primary_color"
                app:endIconMode="password_toggle"
                app:endIconTint="@color/primary_color"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/confirmPasswordInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Confirm New Password"
                    android:inputType="textPassword"
                    android:textSize="16sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Reset Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/resetButton"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="Reset Password"
                android:textSize="16sp"
                android:textStyle="bold"
                android:backgroundTint="@color/primary_color"
                android:textColor="@android:color/white"
                android:layout_marginBottom="24dp"
                android:enabled="false"
                android:alpha="0.5"
                app:cornerRadius="12dp" />

            <!-- Password Requirements -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="32dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="1dp"
                app:cardBackgroundColor="@color/info_background">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Password Requirements:"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="• At least 8 characters long\n• Contains uppercase letters (A-Z)\n• Contains lowercase letters (a-z)\n• Contains numbers (0-9)\n• Contains special characters (!@#$%^&amp;*)"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary"
                        android:lineSpacing="2dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Spacer -->
            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1" />

        </LinearLayout>

    </ScrollView>

</LinearLayout>
