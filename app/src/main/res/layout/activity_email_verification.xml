<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp">

        <!-- Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginTop="40dp"
            android:layout_marginBottom="40dp">

            <ImageView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:src="@drawable/ic_email_verification"
                android:layout_marginBottom="16dp"
                app:tint="@color/primary_color" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Verify Your Email"
                android:textSize="28sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/instructionText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="We've sent a 6-digit verification code to your email address. Please enter it below to verify your account."
                android:textSize="16sp"
                android:textColor="@color/text_secondary"
                android:textAlignment="center"
                android:lineSpacing="4dp" />

        </LinearLayout>

        <!-- Email Display -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:strokeWidth="1dp"
            app:strokeColor="@color/border_color">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="16dp"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_email"
                    app:tint="@color/primary_color"
                    android:layout_marginEnd="12dp" />

                <TextView
                    android:id="@+id/emailDisplay"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="<EMAIL>"
                    android:textSize="16sp"
                    android:textColor="@color/text_primary"
                    android:textStyle="bold" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Verification Code Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:boxStrokeColor="@color/primary_color"
            app:hintTextColor="@color/primary_color"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/verificationCodeInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Enter 6-digit code"
                android:inputType="number"
                android:maxLength="6"
                android:textSize="18sp"
                android:textAlignment="center"
                android:letterSpacing="0.3"
                android:textStyle="bold" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Verify Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/verifyButton"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="Verify"
            android:textSize="16sp"
            android:textStyle="bold"
            android:backgroundTint="@color/primary_color"
            android:textColor="@android:color/white"
            android:layout_marginBottom="24dp"
            android:enabled="false"
            android:alpha="0.5"
            app:cornerRadius="12dp" />

        <!-- Timer and Resend Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginBottom="32dp">

            <TextView
                android:id="@+id/timerText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Didn't receive the code?"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:layout_marginBottom="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/resendButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Resend Code"
                android:textSize="14sp"
                android:textColor="@color/primary_color"
                android:backgroundTint="@android:color/transparent"
                android:enabled="false"
                android:alpha="0.5"
                style="@style/Widget.MaterialComponents.Button.TextButton" />

        </LinearLayout>

        <!-- Spacer -->
        <View
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <!-- Back to Login -->
        <TextView
            android:id="@+id/backToLoginText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="Back to Login"
            android:textSize="16sp"
            android:textColor="@color/primary_color"
            android:textStyle="bold"
            android:padding="12dp"
            android:background="?android:attr/selectableItemBackground" />

    </LinearLayout>

</ScrollView>
