/**
 * Test script to verify the notification fix
 * This script simulates the notification logic to test different scenarios
 */

// Mock Firebase admin and functions for testing
const mockAdmin = {
  firestore: () => ({
    collection: (collectionName) => ({
      doc: (docId) => ({
        get: async () => {
          // Mock data for testing
          const mockUsers = {
            'student123': {
              exists: true,
              data: () => ({
                name: '<PERSON>',
                surname: '<PERSON><PERSON>',
                fcmToken: 'student_fcm_token_123',
                role: 'student'
              })
            },
            'company456': {
              exists: false
            }
          };

          const mockCompanies = {
            'company456': {
              exists: true,
              data: () => ({
                companyName: 'TechCorp Inc',
                fcmToken: 'company_fcm_token_456',
                role: 'company'
              })
            },
            'student123': {
              exists: false
            }
          };

          if (collectionName === 'users') {
            return mockUsers[docId] || { exists: false };
          } else if (collectionName === 'companies') {
            return mockCompanies[docId] || { exists: false };
          }
          
          return { exists: false };
        }
      })
    })
  }),
  messaging: () => ({
    send: async (message) => {
      console.log('📱 NOTIFICATION SENT:');
      console.log(`   To: ${message.token}`);
      console.log(`   Title: ${message.notification.title}`);
      console.log(`   Body: ${message.notification.body}`);
      console.log(`   Channel: ${message.android.notification.channelId}`);
      console.log(`   Data:`, message.data);
      console.log('');
      return 'mock_message_id';
    }
  })
};

// Mock message data for different scenarios
const testScenarios = [
  {
    name: 'Student sends message to Company',
    messageData: {
      senderId: 'student123',
      receiverId: 'company456',
      content: 'Hello, I am interested in the job position',
      type: 'text',
      conversationId: 'conv123'
    },
    expectedRecipient: 'company456',
    expectedRecipientType: 'company',
    expectedSenderName: 'John Doe',
    expectedChannel: 'company_notifications'
  },
  {
    name: 'Company sends message to Student',
    messageData: {
      senderId: 'company456',
      receiverId: 'student123',
      content: 'Thank you for your interest. Let\'s schedule an interview.',
      type: 'text',
      conversationId: 'conv123'
    },
    expectedRecipient: 'student123',
    expectedRecipientType: 'student',
    expectedSenderName: 'TechCorp Inc',
    expectedChannel: 'careerworx_default'
  },
  {
    name: 'Company sends meeting invite to Student',
    messageData: {
      senderId: 'company456',
      receiverId: 'student123',
      content: 'Meeting invitation',
      type: 'meeting_invite',
      conversationId: 'conv123',
      senderName: 'TechCorp Inc'
    },
    expectedRecipient: 'student123',
    expectedRecipientType: 'student',
    expectedSenderName: 'TechCorp Inc',
    expectedChannel: 'meeting_notifications'
  }
];

// Simulate the fixed notification logic
async function simulateNotificationLogic(messageData) {
  console.log('📨 Processing message:', {
    senderId: messageData.senderId,
    receiverId: messageData.receiverId,
    type: messageData.type
  });

  try {
    // Skip notifications for certain message types or if no receiverId
    if (!messageData.receiverId || messageData.receiverId === messageData.senderId) {
      console.log('❌ Skipping notification - no valid receiver or sender is receiver');
      return null;
    }

    // Get the recipient's FCM token (check both users and companies)
    let recipientDoc = await mockAdmin.firestore()
      .collection('users')
      .doc(messageData.receiverId)
      .get();

    let recipientType = 'student';
    
    if (!recipientDoc.exists) {
      // Try companies collection
      recipientDoc = await mockAdmin.firestore()
        .collection('companies')
        .doc(messageData.receiverId)
        .get();
      recipientType = 'company';
    }

    if (!recipientDoc.exists) {
      console.log(`❌ Recipient not found: ${messageData.receiverId}`);
      return null;
    }

    const recipientData = recipientDoc.data();
    const fcmToken = recipientData.fcmToken;
    if (!fcmToken) {
      console.log(`❌ No FCM token for recipient: ${messageData.receiverId}`);
      return null;
    }

    // Get sender information for better notification content
    let senderDoc = await mockAdmin.firestore()
      .collection('users')
      .doc(messageData.senderId)
      .get();

    let senderName = messageData.senderName || 'Someone';
    let senderType = 'student';

    if (!senderDoc.exists) {
      // Try companies collection
      senderDoc = await mockAdmin.firestore()
        .collection('companies')
        .doc(messageData.senderId)
        .get();
      senderType = 'company';
    }

    if (senderDoc.exists) {
      const senderData = senderDoc.data();
      if (senderType === 'company') {
        senderName = senderData.companyName || senderData.name || 'Company';
      } else {
        senderName = `${senderData.name || ''} ${senderData.surname || ''}`.trim() || 'Student';
      }
    }

    // Handle different message types
    let title = 'New Message';
    let body = '';
    let channelId = 'careerworx_default';

    if (messageData.type === 'meeting_invite') {
      title = 'Meeting Invitation';
      body = `${senderName} invited you for an interview`;
      channelId = 'meeting_notifications';
    } else {
      // Regular chat message
      title = `Message from ${senderName}`;
      body = messageData.content || 'You have a new message';
      
      // Use appropriate channel based on recipient type
      channelId = recipientType === 'company' ? 'company_notifications' : 'careerworx_default';
    }

    console.log(`✅ Sending notification to ${recipientType}: ${messageData.receiverId}`);
    console.log(`   From ${senderType}: ${messageData.senderId} (${senderName})`);

    // Send notification
    const message = {
      token: fcmToken,
      notification: {
        title: title,
        body: body,
      },
      data: {
        type: messageData.type || 'chat_message',
        senderId: messageData.senderId,
        receiverId: messageData.receiverId,
        conversationId: messageData.conversationId,
        senderName: senderName,
        senderType: senderType,
        recipientType: recipientType
      },
      android: {
        notification: {
          icon: 'ic_app_logo',
          color: '#2196F3',
          sound: 'default',
          channelId: channelId
        }
      }
    };

    await mockAdmin.messaging().send(message);
    
    return {
      recipientType,
      senderName,
      channelId,
      title,
      body
    };

  } catch (error) {
    console.error('❌ Error in notification logic:', error);
    return null;
  }
}

// Run tests
async function runTests() {
  console.log('🧪 TESTING NOTIFICATION FIX');
  console.log('============================\n');

  for (const scenario of testScenarios) {
    console.log(`🔍 TEST: ${scenario.name}`);
    console.log('----------------------------');
    
    const result = await simulateNotificationLogic(scenario.messageData);
    
    if (result) {
      // Verify expectations
      const checks = [
        {
          name: 'Recipient Type',
          expected: scenario.expectedRecipientType,
          actual: result.recipientType,
          pass: result.recipientType === scenario.expectedRecipientType
        },
        {
          name: 'Sender Name',
          expected: scenario.expectedSenderName,
          actual: result.senderName,
          pass: result.senderName === scenario.expectedSenderName
        },
        {
          name: 'Channel',
          expected: scenario.expectedChannel,
          actual: result.channelId,
          pass: result.channelId === scenario.expectedChannel
        }
      ];

      console.log('📋 VERIFICATION:');
      checks.forEach(check => {
        const status = check.pass ? '✅' : '❌';
        console.log(`   ${status} ${check.name}: ${check.actual} ${check.pass ? '' : `(expected: ${check.expected})`}`);
      });

      const allPassed = checks.every(check => check.pass);
      console.log(`\n🎯 RESULT: ${allPassed ? '✅ PASSED' : '❌ FAILED'}\n`);
    } else {
      console.log('❌ RESULT: FAILED - No notification sent\n');
    }
  }

  console.log('🏁 TESTING COMPLETE');
  console.log('===================');
}

// Run the tests
runTests().catch(console.error);
