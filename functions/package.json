{"name": "careerworx-notifications", "description": "Firebase Functions for CareerWorx push notifications", "scripts": {"lint": "eslint .", "serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "index.js", "dependencies": {"firebase-admin": "^11.8.0", "firebase-functions": "^4.3.1", "nodemailer": "^6.9.8"}, "devDependencies": {"eslint": "^8.15.0", "eslint-config-google": "^0.14.0", "firebase-functions-test": "^3.1.0"}, "private": true}